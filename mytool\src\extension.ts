import * as vscode from 'vscode';
import { MyTool } from './mytool';

export function activate(ctx: vscode.ExtensionContext): void {
  console.log('🚀 MyTool: Extension activating...');

  try {
    // Register a test command first
    const testCommand = vscode.commands.registerCommand('mytool.test', () => {
      vscode.window.showInformationMessage('MyTool extension is active!');
      console.log('🧪 Test command executed - MyTool extension is active');
    });
    ctx.subscriptions.push(testCommand);

    // Register the Language Model Tool
    console.log('🔧 Registering MyTool as Language Model Tool...');

    try {
      const myTool = new MyTool();
      console.log('🔧 Creating tool registration...');

      const toolRegistration = vscode.lm.registerTool('mytool', myTool);
      ctx.subscriptions.push(toolRegistration);

      console.log('✅ Language Model Tool registered successfully');
      console.log('📋 Tool name: mytool');
      console.log('🔧 Tool registration object:', toolRegistration);
      console.log('🔧 Tool instance:', myTool);

      // Verification check
      setTimeout(async () => {
        try {
          console.log('🔍 Checking if tool is discoverable...');

          // Check VS Code version for Language Model Tool support
          const vscodeVersion = vscode.version;
          console.log('🔍 VS Code version:', vscodeVersion);

          // Check if Language Model API is available
          console.log('🔍 Language Model API available:', !!vscode.lm);
          console.log('🔍 Language Model registerTool available:', !!vscode.lm?.registerTool);

          // Try to access the tools
          try {
            if (vscode.lm && 'tools' in vscode.lm) {
              const tools = (vscode.lm as any).tools;
              console.log('🔍 Available tools count:', tools?.length || 'tools property not accessible');

              // Try to find our tool in the list
              if (tools && Array.isArray(tools)) {
                const ourTool = tools.find((tool: any) => tool.name === 'mytool');
                if (ourTool) {
                  console.log('✅ Found our tool in tools list:', ourTool);
                } else {
                  console.log('❌ Our tool not found in tools list');
                  console.log('🔍 Available tool names:', tools.map((t: any) => t.name).slice(0, 10));
                }
              }
            }
          } catch (toolsError) {
            console.log('🔍 Cannot access tools list:', toolsError);
          }

          console.log('🔍 Tool should be available for invocation in Agent/Edit mode');
          console.log('🔍 Try typing "#mytool" in Agent mode to test');

        } catch (checkError) {
          console.error('❌ Tool discovery check failed:', checkError);
        }
      }, 3000);

    } catch (toolError) {
      console.error('❌ Failed to register Language Model Tool:', toolError);
      if (toolError instanceof Error) {
        console.error('❌ Error details:', toolError.message, toolError.stack);
      }
      throw toolError;
    }

    console.log('✅ MyTool: Language Model Tool registered as "mytool"');
    console.log('📋 Tool details:', {
      name: 'mytool',
      type: 'LanguageModelTool'
    });

    console.log('✅ Extension context:', {
      extensionPath: ctx.extensionPath,
      subscriptions: ctx.subscriptions.length
    });

    // Show a notification that the extension is active
    vscode.window.showInformationMessage('🤖 MyTool activated! Use in Agent/Edit mode or reference with #mytool.');

    // Register a test command to manually verify the tool works
    const testToolCommand = vscode.commands.registerCommand('mytool.testTool', async () => {
      try {
        console.log('🧪 Testing Language Model Tool manually...');

        // Create a mock invocation to test the tool
        const myTool = new MyTool();

        // Test prepareInvocation
        const prepareOptions = {
          input: { message: 'Hello from manual test!' },
          tokenBudget: 1000
        };

        const cancellationTokenSource = new vscode.CancellationTokenSource();
        const prepared = await myTool.prepareInvocation(prepareOptions, cancellationTokenSource.token);
        console.log('✅ prepareInvocation succeeded:', prepared);

        // Test invoke
        const invokeOptions = {
          input: { message: 'Hello from manual test!' },
          toolInvocationToken: undefined // We don't have a real token in manual test
        };

        const result = await myTool.invoke(invokeOptions, cancellationTokenSource.token);
        console.log('✅ invoke succeeded:', result);

        vscode.window.showInformationMessage('Language Model Tool test completed - check console for details');

      } catch (error) {
        console.error('❌ Language Model Tool test failed:', error);
        vscode.window.showErrorMessage(`Language Model Tool test failed: ${error}`);
      }
    });

    ctx.subscriptions.push(testToolCommand);

  } catch (error) {
    console.error('❌ Failed to activate extension:', error);
    vscode.window.showErrorMessage(`Failed to activate MyTool: ${error}`);
  }
}

export function deactivate(): void {
  console.log('🛑 MyTool: Extension deactivating...');
}
