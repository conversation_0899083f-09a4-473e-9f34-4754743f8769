# MyTool - Language Model Tool Test Extension

This is a minimal VS Code extension designed to test Language Model Tool registration and help debug why tools might not appear in Agent/Edit mode.

## Purpose

This extension serves as a simple test case to:
1. Verify Language Model Tool registration works correctly
2. Compare with the deepagent implementation to identify issues
3. Test tool invocation in Agent/Edit mode
4. Debug VS Code Language Model Tool API behavior

## Features

- **Simple Echo Tool**: A minimal tool that echoes back the input message
- **Comprehensive Logging**: Detailed console output for debugging registration
- **Manual Testing**: Commands to test tool functionality outside of Agent/Edit mode
- **Minimal Dependencies**: No external dependencies to reduce complexity

## Usage

### Building and Running

1. Navigate to the mytool directory:
   ```bash
   cd mytool
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Compile TypeScript:
   ```bash
   npm run compile
   ```

4. Open in VS Code and press F5 to launch Extension Development Host

### Testing the Tool

1. **Manual Test**: Use the command palette and run "Test MyTool" to verify the extension is active
2. **Tool Test**: Use the command palette and run "MyTool: Test Tool" to manually test the tool implementation
3. **Agent/Edit Mode**: In the Extension Development Host, open Agent or Edit mode and try referencing `#mytool`

### Expected Behavior

- The tool should register as "mytool" 
- It should appear in Agent/Edit mode when typing `#mytool`
- When invoked with `{"message": "test"}`, it should return "Echo: test"

## Debugging

Check the Developer Console (Help > Toggle Developer Tools) for detailed logging:
- Extension activation messages
- Tool registration status
- Tool discovery verification
- Manual test results

## Comparison with DeepAgent

Key differences to investigate:
1. **Complexity**: This tool is much simpler than deepagent
2. **Dependencies**: No external dependencies vs deepagent's MCP and chat utils
3. **Tool Name**: Simple "mytool" vs "deepagent_lmt"
4. **Functionality**: Simple echo vs complex multi-agent orchestration

If this simple tool works but deepagent doesn't, the issue is likely in:
- Complex dependencies
- Tool implementation complexity
- Registration timing
- VS Code API usage patterns
